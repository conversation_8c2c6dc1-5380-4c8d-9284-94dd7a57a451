/**
 * 性能监控工具
 * 用于监控UniverSheet加载性能和识别瓶颈
 */

interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private isEnabled: boolean;

  constructor() {
    this.isEnabled = typeof window !== 'undefined' && 'performance' in window;
  }

  /**
   * 开始计时
   */
  start(name: string): void {
    if (!this.isEnabled) return;

    this.metrics.set(name, {
      name,
      startTime: performance.now(),
    });
  }

  /**
   * 结束计时
   */
  end(name: string): number | null {
    if (!this.isEnabled) return null;

    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`Performance metric "${name}" not found`);
      return null;
    }

    const endTime = performance.now();
    const duration = endTime - metric.startTime;

    metric.endTime = endTime;
    metric.duration = duration;

    // 在开发环境中输出性能信息
    if (process.env.NODE_ENV === 'development') {
      console.log(`⏱️ ${name}: ${duration.toFixed(2)}ms`);
    }

    return duration;
  }

  /**
   * 获取所有性能指标
   */
  getMetrics(): PerformanceMetric[] {
    return Array.from(this.metrics.values()).filter(m => m.duration !== undefined);
  }

  /**
   * 获取特定指标
   */
  getMetric(name: string): PerformanceMetric | undefined {
    return this.metrics.get(name);
  }

  /**
   * 清除所有指标
   */
  clear(): void {
    this.metrics.clear();
  }

  /**
   * 生成性能报告
   */
  generateReport(): string {
    const metrics = this.getMetrics();
    if (metrics.length === 0) {
      return 'No performance metrics available';
    }

    let report = '📊 Performance Report:\n';
    report += '=' .repeat(50) + '\n';

    metrics.forEach(metric => {
      report += `${metric.name}: ${metric.duration!.toFixed(2)}ms\n`;
    });

    const totalTime = metrics.reduce((sum, m) => sum + (m.duration || 0), 0);
    report += '=' .repeat(50) + '\n';
    report += `Total Time: ${totalTime.toFixed(2)}ms\n`;

    return report;
  }

  /**
   * 监控资源加载性能
   */
  monitorResourceLoading(): void {
    if (!this.isEnabled) return;

    // 监控所有资源加载
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.name.includes('univer') || entry.name.includes('univerjs')) {
          const resourceEntry = entry as PerformanceResourceTiming;
          const duration = entry.duration || (resourceEntry.responseEnd - resourceEntry.startTime);
          console.log(`📦 Resource: ${entry.name.split('/').pop()} - ${duration.toFixed(2)}ms`);
        }
      });
    });

    observer.observe({ entryTypes: ['resource'] });
  }

  /**
   * 监控长任务
   */
  monitorLongTasks(): void {
    if (!this.isEnabled || !('PerformanceObserver' in window)) return;

    try {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          console.warn(`🐌 Long Task detected: ${entry.duration.toFixed(2)}ms`);
        });
      });

      observer.observe({ entryTypes: ['longtask'] });
    } catch (error) {
      // Long Task API 可能不被支持
      console.debug('Long Task monitoring not supported');
    }
  }

  /**
   * 监控内存使用
   */
  monitorMemoryUsage(): void {
    if (!this.isEnabled || !('memory' in performance)) return;

    const memory = (performance as any).memory;
    console.log(`💾 Memory Usage:
      Used: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB
      Total: ${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB
      Limit: ${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`);
  }
}

// 创建全局实例
export const perfMonitor = new PerformanceMonitor();

// 便捷函数
export const startTimer = (name: string) => perfMonitor.start(name);
export const endTimer = (name: string) => perfMonitor.end(name);
export const getPerformanceReport = () => perfMonitor.generateReport();

// 自动启动监控（仅在开发环境）
if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
  perfMonitor.monitorResourceLoading();
  perfMonitor.monitorLongTasks();
  
  // 定期输出内存使用情况
  setInterval(() => {
    perfMonitor.monitorMemoryUsage();
  }, 30000); // 每30秒
}
